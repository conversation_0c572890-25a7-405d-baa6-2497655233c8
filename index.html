<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ULTRA-ERP - Modulární ERP systém nové generace</title>
    <meta name="description" content="ULTRA-ERP je modulární ERP systém s AI agenty, flexibilním hostingem a moderní architekturou postavený na Symfony.">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
        }

        .dark-gradient {
            background: linear-gradient(135deg, #2d1b69 0%, #11998e 100%);
        }

        .feature-card {
            transition: all 0.4s ease;
            backdrop-filter: blur(15px);
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s;
            z-index: 1;
        }

        .feature-card:hover::before {
            left: 100%;
        }

        .feature-card:hover {
            transform: translateY(-15px) scale(1.02);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3), 0 0 30px rgba(255, 255, 255, 0.1);
        }

        .feature-icon {
            transition: all 0.3s ease;
            position: relative;
            z-index: 2;
        }

        .feature-card:hover .feature-icon {
            transform: scale(1.1) rotate(5deg);
        }

        .screenshot-container {
            position: relative;
            overflow: hidden;
            border-radius: 20px;
            box-shadow: 0 15px 40px rgba(0,0,0,0.6);
            border: 2px solid rgba(17, 153, 142, 0.2);
            transition: all 0.4s ease;
            background: linear-gradient(145deg, rgba(30, 30, 30, 0.9), rgba(20, 20, 20, 0.8));
        }

        .screenshot-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent, rgba(17, 153, 142, 0.1), transparent);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 1;
        }

        .screenshot-container:hover::before {
            opacity: 1;
        }

        .screenshot-container img {
            transition: transform 0.4s ease;
            position: relative;
            z-index: 0;
        }

        .screenshot-container:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 25px 60px rgba(17, 153, 142, 0.3), 0 0 40px rgba(17, 153, 142, 0.1);
            border-color: rgba(17, 153, 142, 0.4);
        }

        .screenshot-container:hover img {
            transform: scale(1.08);
        }

        .screenshot-overlay {
            position: absolute;
            inset: 0;
            background: linear-gradient(to top, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0.4) 50%, transparent 100%);
            z-index: 2;
        }

        .screenshot-expand {
            position: absolute;
            top: 16px;
            right: 16px;
            background: rgba(17, 153, 142, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 3;
            transition: all 0.3s ease;
            opacity: 0;
            transform: scale(0.8);
        }

        .screenshot-container:hover .screenshot-expand {
            opacity: 1;
            transform: scale(1);
        }

        .screenshot-expand:hover {
            background: rgba(17, 153, 142, 1);
            transform: scale(1.1);
        }

        .tech-badge {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .dark-tech-badge {
            background: rgba(0,0,0,0.3);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.1);
        }

        .floating-element {
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .pulse-glow {
            animation: pulse-glow 2s infinite;
        }

        @keyframes pulse-glow {
            0%, 100% { box-shadow: 0 0 20px rgba(17, 153, 142, 0.4); }
            50% { box-shadow: 0 0 40px rgba(17, 153, 142, 0.8); }
        }

        .glow-text {
            text-shadow: 0 0 20px rgba(17, 153, 142, 0.5);
        }

        .hero-title {
            background: linear-gradient(45deg, #00f5ff, #ff6b6b, #4ecdc4, #45b7d1);
            background-size: 400% 400%;
            animation: gradient-shift 4s ease infinite;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        @keyframes gradient-shift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .floating-icons {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
        }

        .floating-icon {
            position: absolute;
            opacity: 0.3;
            animation: float-around 15s linear infinite;
        }

        @keyframes float-around {
            0% { transform: translateY(100vh) rotate(0deg); }
            100% { transform: translateY(-100px) rotate(360deg); }
        }

        .tech-background {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            opacity: 0.1;
            z-index: 0;
            overflow: hidden;
        }

        .terminal-window {
            position: absolute;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 8px;
            border: 1px solid rgba(0, 255, 255, 0.3);
            font-family: 'Courier New', monospace;
            font-size: 12px;
            color: #00ff00;
            padding: 10px;
            box-shadow: 0 0 20px rgba(0, 255, 255, 0.2);
        }

        .code-snippet {
            position: absolute;
            background: rgba(30, 30, 30, 0.9);
            border-radius: 6px;
            border-left: 3px solid #00ff00;
            font-family: 'Courier New', monospace;
            font-size: 10px;
            color: #ffffff;
            padding: 8px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.3);
        }

        .network-diagram {
            position: absolute;
            width: 200px;
            height: 150px;
            opacity: 0.3;
        }

        .floating-tech {
            animation: float-tech 8s ease-in-out infinite;
        }

        @keyframes float-tech {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-30px) rotate(180deg); }
        }

        .gallery-modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.9);
            backdrop-filter: blur(10px);
        }

        .gallery-modal.active {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .gallery-content {
            max-width: 90%;
            max-height: 90%;
            position: relative;
        }

        .gallery-image {
            width: 100%;
            height: auto;
            border-radius: 12px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.5);
        }

        .gallery-close {
            position: absolute;
            top: -40px;
            right: 0;
            color: white;
            font-size: 30px;
            cursor: pointer;
            background: rgba(0,0,0,0.5);
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .gallery-nav {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            color: white;
            font-size: 24px;
            cursor: pointer;
            background: rgba(0,0,0,0.5);
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .gallery-nav:hover {
            background: rgba(17, 153, 142, 0.8);
        }

        .gallery-prev {
            left: -60px;
        }

        .gallery-next {
            right: -60px;
        }
    </style>
</head>
<body class="bg-gray-900">
    <!-- Navigation -->
    <nav class="fixed w-full z-50 bg-gray-900/90 backdrop-blur-md shadow-lg border-b border-gray-800">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-white">ULTRA ERP</h1>
                </div>
                <div class="hidden md:flex space-x-8">
                    <a href="#features" class="text-gray-300 hover:text-cyan-400 transition-colors">Vlastnosti</a>
                    <a href="#screenshots" class="text-gray-300 hover:text-cyan-400 transition-colors">Screenshoty</a>
                    <a href="#technology" class="text-gray-300 hover:text-cyan-400 transition-colors">Technologie</a>
                    <a href="#pricing" class="text-gray-300 hover:text-cyan-400 transition-colors">Ceník</a>
                    <a href="#contact" class="text-gray-300 hover:text-cyan-400 transition-colors">Kontakt</a>
                </div>
                <button class="md:hidden">
                    <i class="fas fa-bars text-gray-300"></i>
                </button>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="gradient-bg min-h-screen flex items-center justify-center relative overflow-hidden">
        <div class="absolute inset-0 bg-black/20"></div>

        <!-- Tech Background -->
        <div class="tech-background">
            <!-- Terminal Windows -->
            <div class="terminal-window" style="top: 10%; left: 5%; width: 250px; height: 120px;">
                <div style="color: #00ff00;">$ symfony server:start</div>
                <div style="color: #ffff00;">Starting Symfony server...</div>
                <div style="color: #00ff00;">✓ Server running on http://127.0.0.1:8000</div>
                <div style="color: #00ff00;">$ php bin/console doctrine:migrate</div>
                <div style="color: #ffff00;">Migrating up to DoctrineMigrations...</div>
            </div>

            <div class="terminal-window" style="top: 60%; right: 5%; width: 280px; height: 100px;">
                <div style="color: #00ff00;">$ docker-compose up -d</div>
                <div style="color: #ffff00;">Creating ultra-erp_db_1...</div>
                <div style="color: #ffff00;">Creating ultra-erp_redis_1...</div>
                <div style="color: #00ff00;">✓ Services started successfully</div>
            </div>

            <!-- Code Snippets -->
            <div class="code-snippet" style="top: 20%; right: 15%; width: 200px;">
                <div style="color: #ff6b6b;">class</div> <div style="color: #4ecdc4;">ERPController</div> {<br>
                &nbsp;&nbsp;<div style="color: #ff6b6b;">public function</div> <div style="color: #45b7d1;">index()</div><br>
                &nbsp;&nbsp;{<br>
                &nbsp;&nbsp;&nbsp;&nbsp;<div style="color: #ff6b6b;">return</div> $this->render();<br>
                &nbsp;&nbsp;}
            </div>

            <div class="code-snippet" style="bottom: 20%; left: 10%; width: 220px;">
                <div style="color: #ff6b6b;">SELECT</div> * <div style="color: #ff6b6b;">FROM</div> erp_modules<br>
                <div style="color: #ff6b6b;">WHERE</div> status = 'active'<br>
                <div style="color: #ff6b6b;">ORDER BY</div> priority <div style="color: #ff6b6b;">DESC</div>;
            </div>
        </div>

        <!-- Floating Icons Background -->
        <div class="floating-icons">
            <div class="floating-icon text-4xl text-cyan-400" style="left: 10%; animation-delay: 0s;">
                <i class="fab fa-symfony"></i>
            </div>
            <div class="floating-icon text-3xl text-green-400" style="left: 20%; animation-delay: -2s;">
                <i class="fas fa-chart-line"></i>
            </div>
            <div class="floating-icon text-5xl text-purple-400" style="left: 30%; animation-delay: -4s;">
                <i class="fas fa-robot"></i>
            </div>
            <div class="floating-icon text-3xl text-blue-400" style="left: 40%; animation-delay: -6s;">
                <i class="fas fa-database"></i>
            </div>
            <div class="floating-icon text-4xl text-yellow-400" style="left: 50%; animation-delay: -8s;">
                <i class="fas fa-coins"></i>
            </div>
            <div class="floating-icon text-3xl text-red-400" style="left: 60%; animation-delay: -10s;">
                <i class="fas fa-warehouse"></i>
            </div>
            <div class="floating-icon text-4xl text-indigo-400" style="left: 70%; animation-delay: -12s;">
                <i class="fas fa-cogs"></i>
            </div>
            <div class="floating-icon text-3xl text-pink-400" style="left: 80%; animation-delay: -14s;">
                <i class="fas fa-users"></i>
            </div>
            <div class="floating-icon text-5xl text-cyan-400" style="left: 90%; animation-delay: -16s;">
                <i class="fas fa-shield-alt"></i>
            </div>

            <!-- Additional Tech Icons -->
            <div class="floating-icon text-2xl text-orange-400 floating-tech" style="left: 15%; top: 30%; animation-delay: -3s;">
                <i class="fab fa-docker"></i>
            </div>
            <div class="floating-icon text-2xl text-blue-300 floating-tech" style="left: 85%; top: 40%; animation-delay: -7s;">
                <i class="fas fa-terminal"></i>
            </div>
            <div class="floating-icon text-2xl text-green-300 floating-tech" style="left: 25%; top: 70%; animation-delay: -11s;">
                <i class="fas fa-code-branch"></i>
            </div>
        </div>

        <div class="relative z-10 text-center text-white px-4">
            <div class="floating-element">
                <!-- Enhanced Title -->
                <div class="mb-6" data-aos="fade-up">
                    <h1 class="hero-title text-6xl md:text-8xl font-bold text-center">
                        ULTRA ERP
                    </h1>
                </div>

                <div class="max-w-4xl mx-auto">
                    <p class="text-xl md:text-2xl mb-6 leading-relaxed" data-aos="fade-up" data-aos-delay="200">
                        Modulární ERP systém nové generace s AI agenty, flexibilním hostingem a moderní architekturou postavený na Symfony
                    </p>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8 text-center" data-aos="fade-up" data-aos-delay="300">
                        <div class="bg-white/10 backdrop-blur-md rounded-2xl p-4 border border-white/20">
                            <i class="fas fa-puzzle-piece text-2xl text-blue-400 mb-2"></i>
                            <h4 class="font-semibold text-white mb-1">Modulární</h4>
                            <p class="text-sm text-gray-300">Přizpůsobitelné komponenty</p>
                        </div>
                        <div class="bg-white/10 backdrop-blur-md rounded-2xl p-4 border border-white/20">
                            <i class="fas fa-robot text-2xl text-purple-400 mb-2"></i>
                            <h4 class="font-semibold text-white mb-1">AI Powered</h4>
                            <p class="text-sm text-gray-300">Inteligentní automatizace</p>
                        </div>
                        <div class="bg-white/10 backdrop-blur-md rounded-2xl p-4 border border-white/20">
                            <i class="fas fa-cloud text-2xl text-green-400 mb-2"></i>
                            <h4 class="font-semibold text-white mb-1">Cloud Ready</h4>
                            <p class="text-sm text-gray-300">Flexibilní hosting</p>
                        </div>
                    </div>
                    <div class="flex justify-center" data-aos="fade-up" data-aos-delay="500">
                        <a href="#features" class="bg-gradient-to-r from-cyan-500 to-blue-500 text-white px-10 py-4 rounded-full font-semibold hover:from-cyan-400 hover:to-blue-400 transition-all duration-300 pulse-glow inline-flex items-center">
                            <i class="fas fa-arrow-down mr-2"></i>
                            Prozkoumat vlastnosti
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="py-20 bg-gray-800">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold text-white mb-4 glow-text" data-aos="fade-up">
                    Klíčové vlastnosti
                </h2>
                <p class="text-xl text-gray-300 max-w-3xl mx-auto" data-aos="fade-up" data-aos-delay="200">
                    ULTRA-ERP kombinuje nejmodernější technologie s praktickými funkcemi pro efektivní řízení vašeho podnikání
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Feature 1: Modulární architektura -->
                <div class="feature-card bg-gradient-to-br from-blue-600/20 to-blue-800/20 p-8 rounded-3xl shadow-lg border border-blue-500/30" data-aos="fade-up" data-aos-delay="100">
                    <div class="feature-icon w-24 h-24 bg-gradient-to-br from-blue-500/40 to-blue-600/30 rounded-3xl flex items-center justify-center mb-6 border border-blue-400/40">
                        <i class="fas fa-puzzle-piece text-4xl text-blue-300"></i>
                    </div>
                    <div class="mb-4">
                        <span class="text-sm font-semibold text-blue-400 uppercase tracking-wider">Architektura</span>
                        <h3 class="text-3xl font-bold text-white mt-2 mb-4">Modulární systém</h3>
                    </div>
                    <p class="text-gray-300 mb-6 leading-relaxed text-lg">
                        Pevný core modul s možností "přicvaknutí" standardních komponent podle potřeb. Finance, sklad, výroba, CRM a další.
                    </p>
                    <div class="flex flex-wrap gap-3">
                        <span class="bg-blue-500/20 border border-blue-400/40 px-4 py-2 rounded-full text-sm text-blue-300 font-medium">Core Module</span>
                        <span class="bg-blue-600/20 border border-blue-400/40 px-4 py-2 rounded-full text-sm text-blue-300 font-medium">Custom Modules</span>
                    </div>
                </div>

                <!-- Feature 2: Flexibilní hosting -->
                <div class="feature-card bg-gradient-to-br from-green-600/20 to-green-800/20 p-8 rounded-3xl shadow-lg border border-green-500/30" data-aos="fade-up" data-aos-delay="200">
                    <div class="feature-icon w-24 h-24 bg-gradient-to-br from-green-500/40 to-green-600/30 rounded-3xl flex items-center justify-center mb-6 border border-green-400/40">
                        <i class="fas fa-server text-4xl text-green-300"></i>
                    </div>
                    <div class="mb-4">
                        <span class="text-sm font-semibold text-green-400 uppercase tracking-wider">Infrastruktura</span>
                        <h3 class="text-3xl font-bold text-white mt-2 mb-4">Flexibilní hosting</h3>
                    </div>
                    <p class="text-gray-300 mb-6 leading-relaxed text-lg">
                        Self-hosting nebo naša infrastruktura s důkladným monitoringem všech služeb a hardwaru v reálném čase.
                    </p>
                    <div class="flex flex-wrap gap-3">
                        <span class="bg-green-500/20 border border-green-400/40 px-4 py-2 rounded-full text-sm text-green-300 font-medium">Self-hosted</span>
                        <span class="bg-green-600/20 border border-green-400/40 px-4 py-2 rounded-full text-sm text-green-300 font-medium">Cloud</span>
                    </div>
                </div>

                <!-- Feature 3: REST API -->
                <div class="feature-card bg-gradient-to-br from-purple-600/20 to-purple-800/20 p-8 rounded-3xl shadow-lg border border-purple-500/30" data-aos="fade-up" data-aos-delay="300">
                    <div class="feature-icon w-24 h-24 bg-gradient-to-br from-purple-500/40 to-purple-600/30 rounded-3xl flex items-center justify-center mb-6 border border-purple-400/40">
                        <i class="fas fa-code text-4xl text-purple-300"></i>
                    </div>
                    <div class="mb-4">
                        <span class="text-sm font-semibold text-purple-400 uppercase tracking-wider">Integrace</span>
                        <h3 class="text-3xl font-bold text-white mt-2 mb-4">REST API</h3>
                    </div>
                    <p class="text-gray-300 mb-6 leading-relaxed text-lg">
                        Plně zdokumentované REST API pro snadnou integraci externích systémů, mobilních aplikací nebo BI nástrojů.
                    </p>
                    <div class="flex flex-wrap gap-3">
                        <span class="bg-purple-500/20 border border-purple-400/40 px-4 py-2 rounded-full text-sm text-purple-300 font-medium">REST</span>
                        <span class="bg-purple-600/20 border border-purple-400/40 px-4 py-2 rounded-full text-sm text-purple-300 font-medium">Documentation</span>
                    </div>
                </div>
            </div>

            <!-- More Features Row -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mt-8">
                <!-- Feature 4: Symfony -->
                <div class="feature-card bg-gradient-to-br from-red-600/20 to-red-800/20 p-8 rounded-3xl shadow-lg border border-red-500/30" data-aos="fade-up" data-aos-delay="400">
                    <div class="feature-icon w-24 h-24 bg-gradient-to-br from-red-500/40 to-red-600/30 rounded-3xl flex items-center justify-center mb-6 border border-red-400/40">
                        <i class="fab fa-symfony text-4xl text-red-300"></i>
                    </div>
                    <div class="mb-4">
                        <span class="text-sm font-semibold text-red-400 uppercase tracking-wider">Technologie</span>
                        <h3 class="text-3xl font-bold text-white mt-2 mb-4">Symfony Framework</h3>
                    </div>
                    <p class="text-gray-300 mb-6 leading-relaxed text-lg">
                        Postaveno na PHP frameworku Symfony - čistá architektura, modulární codebase a snadná údržba.
                    </p>
                    <div class="flex flex-wrap gap-3">
                        <span class="bg-red-500/20 border border-red-400/40 px-4 py-2 rounded-full text-sm text-red-300 font-medium">Symfony</span>
                        <span class="bg-red-600/20 border border-red-400/40 px-4 py-2 rounded-full text-sm text-red-300 font-medium">PHP</span>
                    </div>
                </div>

                <!-- Feature 5: Security -->
                <div class="feature-card bg-gradient-to-br from-yellow-600/20 to-yellow-800/20 p-8 rounded-3xl shadow-lg border border-yellow-500/30" data-aos="fade-up" data-aos-delay="500">
                    <div class="feature-icon w-24 h-24 bg-gradient-to-br from-yellow-500/40 to-yellow-600/30 rounded-3xl flex items-center justify-center mb-6 border border-yellow-400/40">
                        <i class="fas fa-shield-alt text-4xl text-yellow-300"></i>
                    </div>
                    <div class="mb-4">
                        <span class="text-sm font-semibold text-yellow-400 uppercase tracking-wider">Zabezpečení</span>
                        <h3 class="text-3xl font-bold text-white mt-2 mb-4">Enterprise Security</h3>
                    </div>
                    <p class="text-gray-300 mb-6 leading-relaxed text-lg">
                        VPN nebo HTTPS s WAF, role-based access, 2FA a pravidelné security updates.
                    </p>
                    <div class="flex flex-wrap gap-3">
                        <span class="bg-yellow-500/20 border border-yellow-400/40 px-4 py-2 rounded-full text-sm text-yellow-300 font-medium">HTTPS</span>
                        <span class="bg-yellow-600/20 border border-yellow-400/40 px-4 py-2 rounded-full text-sm text-yellow-300 font-medium">2FA</span>
                    </div>
                </div>

                <!-- Feature 6: User Management -->
                <div class="feature-card bg-gradient-to-br from-indigo-600/20 to-indigo-800/20 p-8 rounded-3xl shadow-lg border border-indigo-500/30" data-aos="fade-up" data-aos-delay="600">
                    <div class="feature-icon w-24 h-24 bg-gradient-to-br from-indigo-500/40 to-indigo-600/30 rounded-3xl flex items-center justify-center mb-6 border border-indigo-400/40">
                        <i class="fas fa-users-cog text-4xl text-indigo-300"></i>
                    </div>
                    <div class="mb-4">
                        <span class="text-sm font-semibold text-indigo-400 uppercase tracking-wider">Správa</span>
                        <h3 class="text-3xl font-bold text-white mt-2 mb-4">User Management</h3>
                    </div>
                    <p class="text-gray-300 mb-6 leading-relaxed text-lg">
                        Robustní uživatelský systém s granulárními právy a detailním logováním pro audit.
                    </p>
                    <div class="flex flex-wrap gap-3">
                        <span class="bg-indigo-500/20 border border-indigo-400/40 px-4 py-2 rounded-full text-sm text-indigo-300 font-medium">RBAC</span>
                        <span class="bg-indigo-600/20 border border-indigo-400/40 px-4 py-2 rounded-full text-sm text-indigo-300 font-medium">Audit</span>
                    </div>
                </div>
            </div>

            <!-- AI & Advanced Features Row -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mt-8">
                <!-- Feature 7: AI Agents -->
                <div class="feature-card bg-gradient-to-br from-purple-600/20 to-pink-600/20 p-8 rounded-3xl shadow-lg border border-purple-500/30" data-aos="fade-up" data-aos-delay="700">
                    <div class="feature-icon w-24 h-24 bg-gradient-to-br from-purple-500/40 to-pink-500/30 rounded-3xl flex items-center justify-center mb-6 border border-purple-400/40">
                        <i class="fas fa-robot text-4xl text-purple-300"></i>
                    </div>
                    <div class="mb-4">
                        <span class="text-sm font-semibold text-purple-400 uppercase tracking-wider">Umělá inteligence</span>
                        <h3 class="text-3xl font-bold text-white mt-2 mb-4">AI Agent Platforma</h3>
                    </div>
                    <p class="text-gray-300 mb-6 leading-relaxed text-lg">
                        Interní platforma pro AI agenty napojená na všechny ERP komponenty. Automatizace úkolů, generování reportů, customer support.
                    </p>
                    <div class="flex flex-wrap gap-3">
                        <span class="bg-purple-500/20 border border-purple-400/40 px-4 py-2 rounded-full text-sm text-purple-300 font-medium">OpenAI API</span>
                        <span class="bg-pink-500/20 border border-pink-400/40 px-4 py-2 rounded-full text-sm text-pink-300 font-medium">Ollama</span>
                    </div>
                </div>

                <!-- Feature 8: OAuth & PWA -->
                <div class="feature-card bg-gradient-to-br from-blue-600/20 to-cyan-600/20 p-8 rounded-3xl shadow-lg border border-blue-500/30" data-aos="fade-up" data-aos-delay="800">
                    <div class="feature-icon w-24 h-24 bg-gradient-to-br from-blue-500/40 to-cyan-500/30 rounded-3xl flex items-center justify-center mb-6 border border-blue-400/40">
                        <i class="fas fa-mobile-alt text-4xl text-blue-300"></i>
                    </div>
                    <div class="mb-4">
                        <span class="text-sm font-semibold text-blue-400 uppercase tracking-wider">Moderní přístup</span>
                        <h3 class="text-3xl font-bold text-white mt-2 mb-4">OAuth & PWA</h3>
                    </div>
                    <p class="text-gray-300 mb-6 leading-relaxed text-lg">
                        Centralizované přihlášení pro všechny aplikace a PWA podpora s offline režimem a push notifikacemi.
                    </p>
                    <div class="flex flex-wrap gap-3">
                        <span class="bg-blue-500/20 border border-blue-400/40 px-4 py-2 rounded-full text-sm text-blue-300 font-medium">OAuth</span>
                        <span class="bg-cyan-500/20 border border-cyan-400/40 px-4 py-2 rounded-full text-sm text-cyan-300 font-medium">PWA</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Screenshots Section -->
    <section id="screenshots" class="py-20 bg-gray-900">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold text-white mb-4 glow-text" data-aos="fade-up">
                    Podívejte se na systém v akci
                </h2>
                <p class="text-xl text-gray-300 max-w-3xl mx-auto" data-aos="fade-up" data-aos-delay="200">
                    Screenshoty z reálného prostředí ULTRA-ERP systému
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10">
                <!-- Main Dashboard -->
                <div class="screenshot-container cursor-pointer" data-aos="zoom-in" data-aos-delay="100" onclick="openGallery(0)">
                    <img src="assets/images/main-dashboard.png" alt="Hlavní dashboard ULTRA ERP" class="w-full h-72 object-cover">
                    <div class="screenshot-overlay"></div>
                    <div class="absolute inset-0 flex items-end z-10">
                        <div class="p-6 text-white">
                            <div class="mb-2">
                                <span class="text-xs font-semibold text-cyan-400 uppercase tracking-wider">Dashboard</span>
                            </div>
                            <h3 class="text-xl font-bold mb-2">Hlavní přehled</h3>
                            <p class="text-sm opacity-90 leading-relaxed">Přehledný dashboard s klíčovými metrikami a rychlým přístupem</p>
                        </div>
                    </div>
                    <div class="screenshot-expand">
                        <i class="fas fa-expand text-white text-sm"></i>
                    </div>
                </div>

                <!-- Storage Component -->
                <div class="screenshot-container cursor-pointer" data-aos="zoom-in" data-aos-delay="200" onclick="openGallery(1)">
                    <img src="assets/images/storage-component.png" alt="Skladový modul ULTRA ERP" class="w-full h-72 object-cover">
                    <div class="screenshot-overlay"></div>
                    <div class="absolute inset-0 flex items-end z-10">
                        <div class="p-6 text-white">
                            <div class="mb-2">
                                <span class="text-xs font-semibold text-green-400 uppercase tracking-wider">Sklad</span>
                            </div>
                            <h3 class="text-xl font-bold mb-2">Skladový systém</h3>
                            <p class="text-sm opacity-90 leading-relaxed">Kompletní správa zásob a skladových operací</p>
                        </div>
                    </div>
                    <div class="screenshot-expand">
                        <i class="fas fa-expand text-white text-sm"></i>
                    </div>
                </div>

                <!-- AI Agent Component -->
                <div class="screenshot-container cursor-pointer" data-aos="zoom-in" data-aos-delay="300" onclick="openGallery(2)">
                    <img src="assets/images/ai-agent-component.png" alt="AI Agent komponenta ULTRA ERP" class="w-full h-72 object-cover">
                    <div class="screenshot-overlay"></div>
                    <div class="absolute inset-0 flex items-end z-10">
                        <div class="p-6 text-white">
                            <div class="mb-2">
                                <span class="text-xs font-semibold text-purple-400 uppercase tracking-wider">AI</span>
                            </div>
                            <h3 class="text-xl font-bold mb-2">AI Agent Platforma</h3>
                            <p class="text-sm opacity-90 leading-relaxed">Inteligentní automatizace a asistence</p>
                        </div>
                    </div>
                    <div class="screenshot-expand">
                        <i class="fas fa-expand text-white text-sm"></i>
                    </div>
                </div>

                <!-- ERP Settings -->
                <div class="screenshot-container cursor-pointer" data-aos="zoom-in" data-aos-delay="400" onclick="openGallery(3)">
                    <img src="assets/images/erp-settings.png" alt="Nastavení ULTRA ERP" class="w-full h-72 object-cover">
                    <div class="screenshot-overlay"></div>
                    <div class="absolute inset-0 flex items-end z-10">
                        <div class="p-6 text-white">
                            <div class="mb-2">
                                <span class="text-xs font-semibold text-yellow-400 uppercase tracking-wider">Konfigurace</span>
                            </div>
                            <h3 class="text-xl font-bold mb-2">Systémová nastavení</h3>
                            <p class="text-sm opacity-90 leading-relaxed">Pokročilá konfigurace a správa</p>
                        </div>
                    </div>
                    <div class="screenshot-expand">
                        <i class="fas fa-expand text-white text-sm"></i>
                    </div>
                </div>

                <!-- Custom Component Examples -->
                <div class="screenshot-container cursor-pointer" data-aos="zoom-in" data-aos-delay="500" onclick="openGallery(4)">
                    <img src="assets/images/custom-component-example.png" alt="Custom komponenta ULTRA ERP" class="w-full h-72 object-cover">
                    <div class="screenshot-overlay"></div>
                    <div class="absolute inset-0 flex items-end z-10">
                        <div class="p-6 text-white">
                            <div class="mb-2">
                                <span class="text-xs font-semibold text-blue-400 uppercase tracking-wider">Custom</span>
                            </div>
                            <h3 class="text-xl font-bold mb-2">Příklad ERP komponenty</h3>
                            <p class="text-sm opacity-90 leading-relaxed">Ukázka standardního ERP modulu</p>
                        </div>
                    </div>
                    <div class="screenshot-expand">
                        <i class="fas fa-expand text-white text-sm"></i>
                    </div>
                </div>

                <div class="screenshot-container cursor-pointer" data-aos="zoom-in" data-aos-delay="600" onclick="openGallery(5)">
                    <img src="assets/images/custom-component-example-2.png" alt="Custom komponenta 2 ULTRA ERP" class="w-full h-72 object-cover">
                    <div class="screenshot-overlay"></div>
                    <div class="absolute inset-0 flex items-end z-10">
                        <div class="p-6 text-white">
                            <div class="mb-2">
                                <span class="text-xs font-semibold text-pink-400 uppercase tracking-wider">Advanced</span>
                            </div>
                            <h3 class="text-xl font-bold mb-2">Pokročilý ERP modul</h3>
                            <p class="text-sm opacity-90 leading-relaxed">Příklad pokročilé komponenty</p>
                        </div>
                    </div>
                    <div class="screenshot-expand">
                        <i class="fas fa-expand text-white text-sm"></i>
                    </div>
                </div>
            </div>

            <!-- Gallery Modal -->
            <div id="galleryModal" class="gallery-modal">
                <div class="gallery-content">
                    <div class="gallery-close" onclick="closeGallery()">
                        <i class="fas fa-times"></i>
                    </div>
                    <div class="gallery-nav gallery-prev" onclick="prevImage()">
                        <i class="fas fa-chevron-left"></i>
                    </div>
                    <div class="gallery-nav gallery-next" onclick="nextImage()">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                    <img id="galleryImage" class="gallery-image" src="" alt="">
                    <div class="text-center mt-4">
                        <h3 id="galleryTitle" class="text-xl font-bold text-white mb-2"></h3>
                        <p id="galleryDescription" class="text-gray-300"></p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Technology Section -->
    <section id="technology" class="py-20 bg-gray-800">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold text-white mb-4 glow-text" data-aos="fade-up">
                    Technologické zázemí
                </h2>
                <p class="text-xl text-gray-300 max-w-3xl mx-auto" data-aos="fade-up" data-aos-delay="200">
                    ULTRA-ERP je postaveno na nejmodernějších technologiích pro maximální výkon a spolehlivost
                </p>
            </div>

            <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8">
                <div class="text-center" data-aos="fade-up" data-aos-delay="100">
                    <div class="w-20 h-20 mx-auto mb-4 bg-red-500/20 rounded-2xl flex items-center justify-center border border-red-500/30">
                        <i class="fab fa-symfony text-3xl text-red-400"></i>
                    </div>
                    <h3 class="font-semibold text-white">Symfony</h3>
                    <p class="text-sm text-gray-400">PHP Framework</p>
                </div>

                <div class="text-center" data-aos="fade-up" data-aos-delay="200">
                    <div class="w-20 h-20 mx-auto mb-4 bg-blue-500/20 rounded-2xl flex items-center justify-center border border-blue-500/30">
                        <i class="fas fa-database text-3xl text-blue-400"></i>
                    </div>
                    <h3 class="font-semibold text-white">Database</h3>
                    <p class="text-sm text-gray-400">MySQL/PostgreSQL</p>
                </div>

                <div class="text-center" data-aos="fade-up" data-aos-delay="300">
                    <div class="w-20 h-20 mx-auto mb-4 bg-green-500/20 rounded-2xl flex items-center justify-center border border-green-500/30">
                        <i class="fas fa-code text-3xl text-green-400"></i>
                    </div>
                    <h3 class="font-semibold text-white">REST API</h3>
                    <p class="text-sm text-gray-400">JSON API</p>
                </div>

                <div class="text-center" data-aos="fade-up" data-aos-delay="400">
                    <div class="w-20 h-20 mx-auto mb-4 bg-purple-500/20 rounded-2xl flex items-center justify-center border border-purple-500/30">
                        <i class="fas fa-robot text-3xl text-purple-400"></i>
                    </div>
                    <h3 class="font-semibold text-white">AI Agents</h3>
                    <p class="text-sm text-gray-400">OpenAI/Ollama</p>
                </div>

                <div class="text-center" data-aos="fade-up" data-aos-delay="500">
                    <div class="w-20 h-20 mx-auto mb-4 bg-yellow-500/20 rounded-2xl flex items-center justify-center border border-yellow-500/30">
                        <i class="fas fa-shield-alt text-3xl text-yellow-400"></i>
                    </div>
                    <h3 class="font-semibold text-white">Security</h3>
                    <p class="text-sm text-gray-400">OAuth/2FA</p>
                </div>

                <div class="text-center" data-aos="fade-up" data-aos-delay="600">
                    <div class="w-20 h-20 mx-auto mb-4 bg-indigo-500/20 rounded-2xl flex items-center justify-center border border-indigo-500/30">
                        <i class="fas fa-mobile-alt text-3xl text-indigo-400"></i>
                    </div>
                    <h3 class="font-semibold text-white">PWA</h3>
                    <p class="text-sm text-gray-400">Progressive Web App</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section id="pricing" class="py-20 bg-gray-800">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div class="mb-16">
                <h2 class="text-4xl font-bold text-white mb-4 glow-text" data-aos="fade-up">
                    Individuální ceník
                </h2>
                <p class="text-xl text-gray-300 max-w-2xl mx-auto" data-aos="fade-up" data-aos-delay="200">
                    Každý projekt je jedinečný, proto vytváříme cenové nabídky přesně na míru vašim potřebám
                </p>
            </div>

            <div class="bg-gradient-to-br from-cyan-600/20 to-blue-600/20 p-10 rounded-3xl shadow-lg border border-cyan-500/30" data-aos="fade-up" data-aos-delay="300">
                <div class="w-20 h-20 bg-gradient-to-br from-cyan-500/40 to-blue-500/30 rounded-3xl flex items-center justify-center mx-auto mb-6 border border-cyan-400/40">
                    <i class="fas fa-calculator text-3xl text-cyan-300"></i>
                </div>

                <h3 class="text-3xl font-bold text-white mb-6">Cena na míru</h3>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                    <div class="text-left">
                        <h4 class="text-lg font-semibold text-cyan-400 mb-3">Co ovlivňuje cenu:</h4>
                        <ul class="space-y-2 text-gray-300">
                            <li class="flex items-center">
                                <i class="fas fa-check text-cyan-400 mr-3"></i>
                                Počet požadovaných modulů
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-cyan-400 mr-3"></i>
                                Složitost custom komponent
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-cyan-400 mr-3"></i>
                                Rozsah integrace s externími systémy
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-cyan-400 mr-3"></i>
                                Požadavky na AI agenty
                            </li>
                        </ul>
                    </div>

                    <div class="text-left">
                        <h4 class="text-lg font-semibold text-cyan-400 mb-3">Zahrnuje:</h4>
                        <ul class="space-y-2 text-gray-300">
                            <li class="flex items-center">
                                <i class="fas fa-check text-cyan-400 mr-3"></i>
                                Kompletní analýzu požadavků
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-cyan-400 mr-3"></i>
                                Vývoj a implementaci
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-cyan-400 mr-3"></i>
                                Školení uživatelů
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-cyan-400 mr-3"></i>
                                Technickou podporu
                            </li>
                        </ul>
                    </div>
                </div>

                <div class="bg-cyan-500/10 border border-cyan-400/30 rounded-2xl p-6 mb-6">
                    <p class="text-lg text-gray-300 leading-relaxed">
                        <i class="fas fa-info-circle text-cyan-400 mr-2"></i>
                        <strong class="text-white">Individuální přístup:</strong> Každá cenová nabídka je vypracována na základě detailní konzultace a analýzy vašich specifických potřeb. Kontaktujte nás pro nezávaznou konzultaci a cenovou kalkulaci.
                    </p>
                </div>

                <button class="bg-cyan-500 text-white px-8 py-4 rounded-full font-semibold hover:bg-cyan-400 transition-colors pulse-glow">
                    <i class="fas fa-comments mr-2"></i>
                    Nezávazná konzultace
                </button>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="py-20 gradient-bg relative overflow-hidden">
        <div class="absolute inset-0 bg-black/20"></div>
        <div class="relative z-10 max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
            <h2 class="text-4xl md:text-5xl font-bold text-white mb-6" data-aos="fade-up">
                Připraveni začít s ULTRA-ERP?
            </h2>
            <p class="text-xl text-white/90 mb-8 max-w-2xl mx-auto" data-aos="fade-up" data-aos-delay="200">
                Kontaktujte nás pro konzultaci a zjistěte, jak ULTRA-ERP může transformovat vaše podnikání
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center" data-aos="fade-up" data-aos-delay="400">
                <button class="bg-cyan-500 text-white px-8 py-4 rounded-full font-semibold hover:bg-cyan-400 transition-colors pulse-glow">
                    <i class="fas fa-envelope mr-2"></i>
                    Kontaktovat nás
                </button>
                <button class="border-2 border-cyan-400 text-cyan-400 px-8 py-4 rounded-full font-semibold hover:bg-cyan-400 hover:text-gray-900 transition-colors">
                    <i class="fas fa-calendar mr-2"></i>
                    Naplánovat demo
                </button>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer id="contact" class="bg-gray-900 text-white py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div class="col-span-1 md:col-span-2">
                    <h3 class="text-2xl font-bold mb-4 text-white">ULTRA ERP</h3>
                    <p class="text-gray-400 mb-6 max-w-md">
                        Modulární ERP systém nové generace s AI agenty, flexibilním hostingem a moderní architekturou postavený na Symfony.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center hover:bg-blue-600 transition-colors">
                            <i class="fab fa-linkedin"></i>
                        </a>
                        <a href="#" class="w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center hover:bg-blue-600 transition-colors">
                            <i class="fab fa-github"></i>
                        </a>
                        <a href="#" class="w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center hover:bg-blue-600 transition-colors">
                            <i class="fas fa-envelope"></i>
                        </a>
                    </div>
                </div>

                <div>
                    <h4 class="text-lg font-semibold mb-4">Produkt</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#features" class="hover:text-white transition-colors">Vlastnosti</a></li>
                        <li><a href="#screenshots" class="hover:text-white transition-colors">Screenshoty</a></li>
                        <li><a href="#technology" class="hover:text-white transition-colors">Technologie</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Dokumentace</a></li>
                    </ul>
                </div>

                <div>
                    <h4 class="text-lg font-semibold mb-4">Kontakt</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#" class="hover:text-white transition-colors">Podpora</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Konzultace</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Partnerství</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Kariéra</a></li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-12 pt-8 text-center text-gray-400">
                <p>&copy; 2024 ULTRA-ERP. Všechna práva vyhrazena.</p>
            </div>
        </div>
    </footer>

    <!-- AOS Animation Library -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

    <!-- Custom JavaScript -->
    <script>
        // Gallery functionality - Define globally first
        const galleryImages = [
            {
                src: 'assets/images/main-dashboard.png',
                title: 'Hlavní dashboard',
                description: 'Centrální přehled celého ERP systému s real-time metrikami, rychlými akcemi a přizpůsobitelnými widgety. Dashboard poskytuje okamžitý přehled o stavu firmy, prodejích, skladových zásobách a klíčových ukazatelích výkonnosti.'
            },
            {
                src: 'assets/images/storage-component.png',
                title: 'Skladový modul',
                description: 'Kompletní řešení pro správu skladů včetně příjmu a výdeje zboží, inventury, sledování sériových čísel a expiračních dat. Modul podporuje více skladů, automatické objednávání při dosažení minimálních stavů a integraci s čárovými kódy.'
            },
            {
                src: 'assets/images/ai-agent-component.png',
                title: 'AI Agent platforma',
                description: 'Pokročilá platforma pro AI asistenty integrovaná do všech modulů ERP systému. Agenti automatizují rutinní úkoly, generují inteligentní reporty, poskytují prediktivní analýzy a asistují při rozhodování na základě historických dat.'
            },
            {
                src: 'assets/images/erp-settings.png',
                title: 'Systémová nastavení',
                description: 'Centralizované nastavení celého ERP systému včetně uživatelských rolí, oprávnění, bezpečnostních politik, integračních nastavení a konfigurace jednotlivých modulů. Umožňuje granulární správu přístupových práv a audit všech změn.'
            },
            {
                src: 'assets/images/custom-component-example.png',
                title: 'Příklad ERP komponenty',
                description: 'Ukázka standardní ERP komponenty pro správu obchodních případů. Toto je pouze příklad jednoho z mnoha modulů, které lze do systému integrovat. Každá komponenta je navržena s důrazem na uživatelskou přívětivost a efektivitu práce.'
            },
            {
                src: 'assets/images/custom-component-example-2.png',
                title: 'Pokročilý ERP modul',
                description: 'Další příklad ERP komponenty demonstrující pokročilé funkcionality a možnosti přizpůsobení. Komponenty lze upravovat podle specifických potřeb firmy, přidávat vlastní pole, workflow a business logiku pro maximální efektivitu procesů.'
            }
        ];

        let currentImageIndex = 0;

        function openGallery(index) {
            currentImageIndex = index;
            const modal = document.getElementById('galleryModal');
            const image = document.getElementById('galleryImage');
            const title = document.getElementById('galleryTitle');
            const description = document.getElementById('galleryDescription');

            const currentImage = galleryImages[index];
            image.src = currentImage.src;
            image.alt = currentImage.title;
            title.textContent = currentImage.title;
            description.textContent = currentImage.description;

            modal.classList.add('active');
            document.body.style.overflow = 'hidden';
        }

        function closeGallery() {
            const modal = document.getElementById('galleryModal');
            modal.classList.remove('active');
            document.body.style.overflow = 'auto';
        }

        function nextImage() {
            currentImageIndex = (currentImageIndex + 1) % galleryImages.length;
            updateGalleryImage();
        }

        function prevImage() {
            currentImageIndex = (currentImageIndex - 1 + galleryImages.length) % galleryImages.length;
            updateGalleryImage();
        }

        function updateGalleryImage() {
            const image = document.getElementById('galleryImage');
            const title = document.getElementById('galleryTitle');
            const description = document.getElementById('galleryDescription');

            const currentImage = galleryImages[currentImageIndex];
            image.src = currentImage.src;
            image.alt = currentImage.title;
            title.textContent = currentImage.title;
            description.textContent = currentImage.description;
        }

        // Initialize AOS
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true,
            offset: 100
        });

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Mobile menu toggle
        const mobileMenuButton = document.querySelector('.md\\:hidden button');
        const mobileMenu = document.createElement('div');
        mobileMenu.className = 'md:hidden bg-gray-900 shadow-lg absolute top-full left-0 right-0 z-40 border-t border-gray-800';
        mobileMenu.innerHTML = `
            <div class="px-4 py-2 space-y-2">
                <a href="#features" class="block py-2 text-gray-300 hover:text-cyan-400">Vlastnosti</a>
                <a href="#screenshots" class="block py-2 text-gray-300 hover:text-cyan-400">Screenshoty</a>
                <a href="#technology" class="block py-2 text-gray-300 hover:text-cyan-400">Technologie</a>
                <a href="#pricing" class="block py-2 text-gray-300 hover:text-cyan-400">Ceník</a>
                <a href="#contact" class="block py-2 text-gray-300 hover:text-cyan-400">Kontakt</a>
            </div>
        `;

        let mobileMenuOpen = false;
        mobileMenuButton.addEventListener('click', () => {
            if (!mobileMenuOpen) {
                mobileMenuButton.parentElement.parentElement.appendChild(mobileMenu);
                mobileMenuOpen = true;
            } else {
                mobileMenu.remove();
                mobileMenuOpen = false;
            }
        });

        // Navbar background on scroll
        window.addEventListener('scroll', () => {
            const navbar = document.querySelector('nav');
            if (window.scrollY > 50) {
                navbar.classList.add('bg-gray-900/95');
                navbar.classList.remove('bg-gray-900/90');
            } else {
                navbar.classList.add('bg-gray-900/90');
                navbar.classList.remove('bg-gray-900/95');
            }
        });

        // Parallax effect for hero section
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const parallax = document.querySelector('.gradient-bg');
            const speed = scrolled * 0.5;
            parallax.style.transform = `translateY(${speed}px)`;
        });

        // Counter animation for stats (if you want to add stats later)
        function animateCounter(element, target, duration = 2000) {
            let start = 0;
            const increment = target / (duration / 16);
            const timer = setInterval(() => {
                start += increment;
                element.textContent = Math.floor(start);
                if (start >= target) {
                    element.textContent = target;
                    clearInterval(timer);
                }
            }, 16);
        }

        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-fade-in');
                }
            });
        }, observerOptions);

        // Observe all feature cards
        document.querySelectorAll('.feature-card').forEach(card => {
            observer.observe(card);
        });

        // Gallery event listeners
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeGallery();
            } else if (e.key === 'ArrowRight') {
                nextImage();
            } else if (e.key === 'ArrowLeft') {
                prevImage();
            }
        });

        // Close gallery when clicking outside the image
        document.addEventListener('DOMContentLoaded', function() {
            const galleryModal = document.getElementById('galleryModal');
            if (galleryModal) {
                galleryModal.addEventListener('click', function(e) {
                    if (e.target === this) {
                        closeGallery();
                    }
                });
            }
        });


    </script>
</body>
</html>
